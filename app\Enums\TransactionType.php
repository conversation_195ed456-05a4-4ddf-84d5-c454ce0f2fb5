<?php

namespace App\Enums;

enum TransactionType: string
{
 case DEPOSIT = 'DEPOSIT';
 case WITHDRAWAL = 'WITHDRAWAL';
 case TOP_UP = 'TOP_UP';
 case REFAUND = 'REFAUND';
 
    public function label(): string
    {
        return match ($this) {
            self::DEPOSIT => __('enum-label.deposit'),
            self::WITHDRAWAL => __('enum-label.withdrawal'),
            self::TOP_UP => __('enum-label.top_up'),
            self::REFAUND => __('enum-label.refaund'),
        };
    }



   
}
