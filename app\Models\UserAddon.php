<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class UserAddon extends Model
{
    protected $fillable = [
        'user_id', 'addon_id', 'expires_at', 'price'
    ];

    protected $casts = [
        'addon_id' => 'integer',
        'user_id' => 'integer',
        'price' => 'float',
        'expires_at' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
      
    ];

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function addon()
    {
        return $this->belongsTo(Addon::class);
    }

}
