<?php

namespace App\Filament\Admin\Auth;

use DiogoGPinto\AuthUIEnhancer\Pages\Auth\Concerns\HasCustomLayout;
use Filament\Forms\Form;
use Filament\Pages\Auth\Login as BaseLogin;

/**
 * CustomLogin
 *
 * This class extends Filament's base login page to provide a customized
 * authentication  for the admin panel. 
 */
class CustomLogin extends BaseLogin
{
  
    use HasCustomLayout;

    /**
     * Custom view template for the login page
     */
    protected static string $view = 'filament.pages.auth.custom-login';

    /**
     * Disable the brand logo display on the login page
     *      *
     * @return bool False to hide the brand logo
     */
    protected function hasBrand(): bool
    {
        return false;
    }

    /**
     * Get the page title for the login page
     *
     * Returns an empty string to remove the default page title,
     * allowing the custom view to handle title display.
     *
     * @return string Empty string to hide default title
     */
    public function getTitle(): string
    {
        return '';
    }

    /**
     * Get the heading text for the login form
     *
     * Returns an empty string to remove the default heading,
     * giving full control to the custom view template.
     *
     * @return string Empty string to hide default heading
     */
    public function getHeading(): string
    {
        return '';
    }

    /**
     * Configure the login form
     *
     * This method customizes the login form 
     *
     * @param Form $form The form instance to configure
     * @return Form The configured form 
     */
    public function form(Form $form): Form
    {
        $dir=app()->getLocale() === 'ar' ? 'rtl' : 'ltr';
        return $form
            ->schema([
                // Email input field 
                $this->getEmailFormComponent()
                    ->label(__('filament-panels.login.fields.email')) 
                    ->extraInputAttributes(['dir' => $dir]), 

                // Password input field
                $this->getPasswordFormComponent()
                    ->label(__('filament-panels.login.fields.password')) 
                    ->extraInputAttributes(['dir' => $dir]), 

                // Remember me checkbox 
                $this->getRememberFormComponent()
                    ->label(__('filament-panels.login.fields.remember')), 
            ])
            ->columns(1); // Single column layout for better mobile experience
    }
}