<?php

namespace App\Enums;

enum DifficultyLevel: string
{
    case EASY = 'EASY';
    case MEDIUM = 'MEDIUM';
    case HARD = 'HARD';
    case EXTREME = 'EXTREME';

    public function label(): string
    {
        return match ($this) {
            self::EASY => __('enum-label.easy'),
            self::MEDIUM => __('enum-label.medium'),
            self::HARD => __('enum-label.hard'),
            self::EXTREME => __('enum-label.extreme'),
        };
    }



   
}
