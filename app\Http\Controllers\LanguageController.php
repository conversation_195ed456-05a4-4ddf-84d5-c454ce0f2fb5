<?php

namespace App\Http\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Session;

class LanguageController extends Controller
{ 
    /**
     * Switch the language for the user and store in session.
     *
     * @param  string  $locale The language to switch to (en or ar)
     */
    public function switchLang(string $locale): RedirectResponse
    {
        if (!in_array($locale, ['en', 'ar'])) {
            abort(400, 'Invalid language selection');
        }

        // Store the locale in session
        Session::put('locale', $locale);
        
        // Set the application locale
        App::setLocale($locale);
        
        return back();
    }
}