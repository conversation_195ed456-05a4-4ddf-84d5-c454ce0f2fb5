<?php

namespace App\Enums;

enum BillingCycle: string
{
    case WEEKLY = 'WEEKLY';
    case MONTHLY = 'MONTHLY';
    case YEARLY = 'YEARLY';
    
    public function label(): string
    {
        return match ($this) {
            self::WEEKLY => __('enum-label.weekly'),
            self::MONTHLY => __('enum-label.monthly'),
            self::YEARLY => __('enum-label.yearly'),
        };
    }




  
}
