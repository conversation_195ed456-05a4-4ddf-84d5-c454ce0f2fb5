<?php

namespace App\Jobs;

use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Http;

/**
 * Job class for sending WhatsApp messages via WhatsApp Business API
 * 
 * This job handles the asynchronous sending of WhatsApp messages, specifically OTP codes.
 * It implements <PERSON><PERSON>'s queue system to handle message sending in the background,
 * preventing API timeouts and improving user experience.
 */
class SendWhatsAppMessageJob implements ShouldQueue
{
    use Queueable;
    
    /**
     * The recipient's phone number
     * @var string
     */
    protected $phone;
    
    /**
     * The OTP code to send
     * @var string|int
     */
    protected $otp;

    /**
     * Create a new job instance
     * 
     * @param string $phone - The recipient's phone number (should include country code)
     * @param string|int $otp - The OTP code to send via WhatsApp
     */
    public function __construct($phone, $otp)
    {
        $this->phone = $phone;
        $this->otp = $otp;
    }

    /**
     * Execute the job to send WhatsApp message
     * 
     * This method:
     * - Constructs the WhatsApp Business API payload using the OTP template
     * - Sends the message via HTTP POST to WhatsApp API
     * - Uses the configured access token for authentication
     * - Logs any errors that occur during the sending process
     * 
     * @return void
     */
    public function handle(): void
    {
        try {
            $to = $this->phone;

            //API URL for sending the message
            $url = config('api.whatsapp_api_url') . config('api.whatsapp_phone_id') . "/messages";

            $payload = [
                "messaging_product" => "whatsapp",
                "to" => $to,
                "type" => "template",
                "template" => [
                    "name" => 'otp',
                    "language" => ["code" => "ar"],
                    "components" => [
                        [
                            "type" => "body",
                            "parameters" => [
                                ["type" => "text", "text" => (string) $this->otp]
                            ]
                        ],
                        [
                            "type" => "button",
                            "sub_type" => "url",
                            "index" => "0",
                            "parameters" => [
                                ["type" => "text", "text" => (string) $this->otp]
                            ]
                        ]
                    ]
                ]
            ];

            // Send the request to the WhatsApp API with authentication
            $response = Http::withToken(config('api.whatsapp_access_token'))
                ->post($url, $payload);

        } catch (\Exception $e) {
            Log::error('WhatsApp message sending failed', [
                'error' => $e->getMessage()
                ]
            );
        }
    }
}
