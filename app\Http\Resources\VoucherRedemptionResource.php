<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use App\Http\Resources\VoucherResource;
use App\Http\Resources\UserResource;
use App\Http\Resources\TransactionResource;

class VoucherRedemptionResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'redeemed_at' => $this->redeemed_at,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
            'voucher' => new VoucherResource($this->whenLoaded('voucher')),
            'user' => new UserResource($this->whenLoaded('user')),
            'transaction' => new TransactionResource($this->whenLoaded('transaction')),
        ];
    }
}
