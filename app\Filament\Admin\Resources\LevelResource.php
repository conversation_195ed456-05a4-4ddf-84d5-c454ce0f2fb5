<?php

namespace App\Filament\Admin\Resources;

use App\Enums\StageType;
use App\Filament\Admin\Resources\LevelResource\Pages;
use App\Models\Level;
use Filament\Forms;
use Filament\Forms\Components\Section;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Validation\Rule;

class LevelResource extends Resource
{
    protected static ?string $model = Level::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    public static function getModelLabel(): string
    {
        return __('filament-panels.levels.single');
    }

    public static function getPluralModelLabel(): string
    {
        return __('filament-panels.levels.title');
    }

    public static function getNavigationGroup(): ?string
    {
        return __('filament-panels.groups.courses');
    }

    public static function form(Form $form): Form
    {
        return $form->schema([
            Section::make()->schema([
                Forms\Components\TextInput::make('name')
                    ->label(__('filament-panels.fields.name'))
                    ->required()
                    ->maxLength(255)
                    ->validationMessages([
                        'required' => __('validation.required'),
                        'max'      => __('validation.max.string'),
                        'unique'   => __('validation.unique'),
                    ])
                    // unique داخل نفس ال-stage
                    ->rule(function (callable $get) {
                        $stageId = $get('stage_id');
                        return Rule::unique('levels', 'name')
                            ->ignore(request()->route('record')) 
                            ->where(fn ($q) => $q->where('stage_id', $stageId));
                    }),

                Forms\Components\Select::make('stage_id')
                    ->label(__('filament-panels.fields.stage'))
                    ->relationship('stage', 'name')
                    ->searchable()
                    ->preload()
                    ->required()
                    ->validationMessages([
                        'required' => __('validation.required'),
                    ]),
            ]),
        ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('name')
                    ->label(__('filament-panels.fields.name'))
                    ->sortable()
                    ->searchable(),

                Tables\Columns\TextColumn::make('stage.name')
                    ->label(__('filament-panels.fields.stage'))
                    ->badge()
                    ->color(fn ($record) => $record->stage?->type?->color() ?? 'secondary')
                    ->sortable()
                    ->toggleable(),

                Tables\Columns\TextColumn::make('created_at')
                    ->label(__('filament-panels.fields.created_at'))
                    ->dateTime('Y-m-d H:i')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),

                Tables\Columns\TextColumn::make('updated_at')
                    ->label(__('filament-panels.fields.updated_at'))
                    ->dateTime('Y-m-d H:i')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])

            ->filters([

                Tables\Filters\SelectFilter::make('stage_id')
                    ->label(__('filament-panels.fields.stage'))
                    ->relationship('stage', 'name')
                    ->indicator(__('filament-panels.fields.stage')),


                Tables\Filters\SelectFilter::make('stage_type')
                    ->label(__('filament-panels.fields.type') )
                    ->options([
                        StageType::ACADEMIC->value     => StageType::ACADEMIC->label(),
                        StageType::PROFESSIONAL->value => StageType::PROFESSIONAL->label(),
                    ])
                    ->query(function (Builder $query, array $data) {
                        if (filled($data['value'])) {
                            $query->whereHas('stage', fn (Builder $q) =>
                                $q->where('type', $data['value'])
                            );
                        }
                    })
                    ->indicator(__('filament-panels.fields.type')),
            ])

            ->actions([
                Tables\Actions\EditAction::make(),
            ])

            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])

            ->emptyStateIcon('heroicon-o-rectangle-stack')
            ->emptyStateHeading(__('filament-panels.levels.empty.title') ?? 'No Levels yet')
            ->emptyStateDescription(__('filament-panels.levels.empty.desc') ?? 'Create a level to get started.')
            ->emptyStateActions([
                Tables\Actions\CreateAction::make(),
            ])

            ->defaultSort('created_at', 'desc')
            ->paginated([10, 25, 50, 100]);
    }

    public static function getRelations(): array
    {
        return [
            // لاحقًا: SubjectsRelationManager::class
        ];
    }

    public static function getPages(): array
    {
        return [
            'index'  => Pages\ListLevels::route('/'),
            'create' => Pages\CreateLevel::route('/create'),
            'edit'   => Pages\EditLevel::route('/{record}/edit'),
        ];
    }
}
