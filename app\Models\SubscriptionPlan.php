<?php

namespace App\Models;

use App\Enums\BillingCycle;
use App\Enums\Currency;
use Illuminate\Database\Eloquent\Model;

class SubscriptionPlan extends Model
{
    protected $fillable = [
        'name',
        'description',
        'price',
        'currency',
        'billing_cycle',
        'trial_days'
    ];
    protected $casts = [
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'price' => 'float',
        'currency' => Currency::class,
        'billing_cycle' => BillingCycle::class,
        'trial_days' => 'integer',
    ];
    public function subscriptions()
    {
        return $this->hasMany(UserSubscription::class, 'plan_id');
    }
    
    public function accessibleContents()
    {
        return $this->belongsToMany(Content::class, 'content_subscription_plan', 'subscription_plan_id', 'content_id');
    }
}
