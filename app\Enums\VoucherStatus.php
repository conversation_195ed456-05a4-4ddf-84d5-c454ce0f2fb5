<?php

namespace App\Enums;

enum VoucherStatus: string
{               
    case ACTIVE = 'ACTIVE';
    case USED = 'USED';
    case EXPIRED = 'EXPIRED';

    public function label(): string
    {
        return match($this) {
            self::ACTIVE => __('enum-label.active'),
            self::USED => __('enum-label.used'),
            self::EXPIRED => __('enum-label.expired'),
        };
    }

  
}
