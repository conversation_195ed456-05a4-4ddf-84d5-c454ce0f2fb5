<?php

namespace App\Models;

use App\Enums\VoucherStatus;
use Illuminate\Database\Eloquent\Model;

class Voucher extends Model
{
    protected $fillable = [
        'serial_number', 'description',  'face_value',
        'valid_from', 'expires_at', 'status', 'pin_hash'
    ];
    protected $casts = [
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'valid_from' => 'datetime',
        'expires_at' => 'datetime',
        'status' => VoucherStatus::class,
    ];


    public function redemptions()
    {
        return $this->hasMany(VoucherRedemption::class);
    }
}
