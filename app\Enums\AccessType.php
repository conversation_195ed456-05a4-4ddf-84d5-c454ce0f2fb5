<?php

namespace App\Enums;

enum AccessType: string
{
    case FREE_CONTENT = 'FREE_CONTENT';
    case ADDON = 'ADDON';
    case PLAN = 'PLAN';

    public function label(): string
    {
        return match ($this) {
            self::FREE_CONTENT => __('enum-label.free_content'),
            self::ADDON => __('enum-label.addon'),
            self::PLAN => __('enum-label.plan'),
        };
    }



   
}
