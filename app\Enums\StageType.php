<?php

namespace App\Enums;

enum StageType: string
{
    case ACADEMIC = 'ACADEMIC';
    case PROFESSIONAL = 'PROFESSIONAL';

    public function label(): string
    {
        return match ($this) {
            self::ACADEMIC => __('enum-label.academic'),
            self::PROFESSIONAL => __('enum-label.professional'),
        };
    }
    public function color(): string
    {
        return match ($this) {
            self::ACADEMIC => 'primary',
            self::PROFESSIONAL => 'warning',
        };
    }
   
}
