<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('transactions', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained('users')->onDelete('cascade');
            $table->enum('type', ['DEPOSIT', 'WITHDRAWAL', 'TOP_UP','REFAUND']);
            $table->decimal('amount', 10, 2);
            $table->enum('status', ['PENDING', 'COMPLETED', 'FAILED', 'CANCELED']);
            $table->text('description')->nullable();
            $table->foreignId('user_subscription_id')->nullable()->constrained('user_subscriptions')->onDelete('cascade');
            $table->enum('getway', ['STRIPE', 'TLYNC','DCB'])->nullable();
            $table->string('getway_external_id')->nullable();
            $table->json('metadata')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('transactions');
    }
};
