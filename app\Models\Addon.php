<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Addon extends Model
{
    protected $fillable = ['name', 'description'];

    protected $casts = [
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    public function contents()
    {
        return $this->belongsToMany(Content::class, 'content_addons', 'addon_id', 'content_id');
    }
  
    public function userAddons()
    {
        return $this->hasMany(UserAddon::class);
    }
}
