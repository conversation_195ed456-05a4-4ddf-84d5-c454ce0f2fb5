<?php

namespace Database\Seeders;

use App\Enums\StageType;
use App\Models\Stage;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class StageSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $stages = [
            ['name' => 'المرحلة الابتدائية', 'type' => StageType::ACADEMIC],
            ['name' => 'المرحلة الاعدادية', 'type' => StageType::ACADEMIC],
            ['name' => 'المرحلة الثانوية', 'type' => StageType::ACADEMIC],
        ];
        foreach ($stages as $stage) {
            Stage::create($stage);
        }
        
    }
}
