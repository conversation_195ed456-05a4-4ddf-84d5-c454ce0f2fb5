<?php

namespace Database\Seeders;

use App\Models\Level;
use App\Models\Stage;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class LevelSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $stageOneId = Stage::where('name', 'المرحلة الابتدائية')->first()->id;
        $stageTwoId = Stage::where('name', 'المرحلة الاعدادية')->first()->id;
        $stageThreeId = Stage::where('name', 'المرحلة الثانوية')->first()->id;
        
        $levels = [
            ['name' => 'الصف الاول الابتدائي', 'stage_id' => $stageOneId],
            ['name' => 'الصف الثاني الابتدائي', 'stage_id' => $stageOneId],
            ['name' => 'الصف الثالث الابتدائي', 'stage_id' => $stageOneId],
            ['name' => 'الصف الرابع الابتدائي', 'stage_id' => $stageOneId],
            ['name' => 'الصف الخامس الابتدائي', 'stage_id' => $stageOneId],
            ['name' => 'الصف السادس الابتدائي', 'stage_id' => $stageOneId],
            ['name' => 'الصف الاول الاعدادي', 'stage_id' => $stageTwoId],
            ['name' => 'الصف الثاني الاعدادي', 'stage_id' => $stageTwoId],
            ['name' => 'الصف الثالث الاعدادي', 'stage_id' => $stageTwoId],
            ['name' => 'الصف الاول الثانوي', 'stage_id' => $stageThreeId],
            ['name' => 'الصف الثاني الثانوي', 'stage_id' => $stageThreeId],
            ['name' => 'الصف الثالث الثانوي', 'stage_id' => $stageThreeId],
        ];
        foreach ($levels as $level) {
            Level::create($level);
        }
    }
}
