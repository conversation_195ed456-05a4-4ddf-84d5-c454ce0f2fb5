<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use App\Http\Resources\UserSubscriptionResource;
use App\Http\Resources\WalletResource;

class TransactionResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'amount' => $this->amount,
            'type' => $this->type->value,
            'status' => $this->status->value,
            'description' => $this->description,
            'gateway' => $this->gateway->value,
            'gateway_external_ref' => $this->gateway_external_ref,
            'meta_data' => $this->meta_data,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
            'wallet' => new WalletResource($this->whenLoaded('wallet')),
            'user_subscription' => new UserSubscriptionResource($this->whenLoaded('userSubscription')),
        ];
    }
}
