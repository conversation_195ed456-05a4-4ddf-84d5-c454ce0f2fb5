<?php

namespace App\Models;

use App\Enums\TransactionGetway;
use App\Enums\TransactionStatus;
use App\Enums\TransactionType;
use Illuminate\Database\Eloquent\Model;

class Transaction extends Model
{
    protected $fillable = [
        'wallet_id', 'type', 'amount', 'status', 'description', 'user_subscription_id', 'getway', 'getway_external_id', 'metadata'
    ];

    protected $casts = [
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'wallet_id' => 'integer',
        'user_subscription_id' => 'integer',
        'amount' => 'float',
        'status' => TransactionStatus::class,
        'type' => TransactionType::class,
        'getway' => TransactionGetway::class,
     
    ];

    public function userSubscription()
    {
        return $this->belongsTo(UserSubscription::class);
    }

    public function wallet()
    {
        return $this->belongsTo(Wallet::class);
    }

    public function voucherRedemption()
    {
        return $this->hasMany(VoucherRedemption::class);
    }

    

}
