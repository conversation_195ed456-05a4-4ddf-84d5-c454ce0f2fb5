<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class StudentAnswer extends Model
{
    
    protected $fillable = [
        'student_assessment_id', 'question_id', 'answer_text', 'is_correct'
    ];

    protected $casts = [
        'is_correct' => 'boolean',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'student_assessment_id' => 'integer',
        'question_id' => 'integer',
    ];

    public function studentAssessment()
    {
        return $this->belongsTo(StudentAssessment::class, 'student_assessment_id');
    }

    public function question()
    {
        return $this->belongsTo(Question::class);
    }
}
