<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class ContentRate extends Model
{
    protected $fillable = [
        'content_id', 'user_id', 'rate', 'review'
    ];

    protected $casts = [
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'content_id' => 'integer',
        'user_id' => 'integer',
    ];

    public function content()
    {
        return $this->belongsTo(Content::class);
    }

    public function student()
    {
        return $this->belongsTo(User::class);
    }

}
