<?php

namespace Database\Seeders;

use App\Models\Level;
use App\Models\Subject;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class SubjectSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $subjects = [
            ['name' => 'الرياضيات'],
            ['name' => 'العلوم'],
            ['name' => 'اللغة العربية'],
            ['name' => 'اللغة الانجليزية'],
        ];

        $levels = Level::all();
        if ($levels->count() < 1) {
            return;
        }
        
        foreach ($subjects as $subject) {
            foreach ($levels as $level) {
                Subject::create(['name' => $subject['name'], 'level_id' => $level->id]);
            }
        }
    }
}
