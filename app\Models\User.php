<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;

use Filament\Models\Contracts\FilamentUser;
use Filament\Panel;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Spatie\Permission\Traits\HasRoles;

class User extends Authenticatable implements FilamentUser
{
    /** @use HasFactory<\Database\Factories\UserFactory> */
    use HasFactory, Notifiable;
    use HasRoles;

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'name',
        'email',
        'password',
        'phone_number',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var list<string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'password' => 'hashed',
            'created_at' => 'datetime',
            'updated_at' => 'datetime',
            'phone_number' => 'integer',
        ];
    }
    public function wallet()
    {
        return $this->hasOne(Wallet::class);
    }

    public function subscriptions()
    {
        return $this->hasMany(UserSubscription::class);
    }

    public function contents()
    {
        return $this->hasMany(Content::class, 'uploaded_by');
    }

    public function assessments()
    {
        return $this->hasMany(Assessment::class, 'created_by');
    }
    public function rates()
    {
        return $this->hasMany(ContentRate::class, 'user_id');
    }
    public function profiles()
    {
        return $this->hasMany(Profile::class);
    }
    public function questions()
    {
        return $this->hasMany(Question::class, 'created_by');
    }
    public function studentAssessments()
    {
        return $this->hasMany(StudentAssessment::class, 'student_id');
    }
    public function addons()
    {
        return $this->hasMany(UserAddon::class);
    }
    public function voucherRedemptions()
    {
        return $this->hasMany(VoucherRedemption::class);
    }

    public function canAccessPanel(Panel $panel): bool
    {

        return match ($panel->getId()) {
            'admin' => $this->role === $this->hasRole('admin')||$this->hasRole('super_admin'),
            default => false,
        };
    }

    
    
}

