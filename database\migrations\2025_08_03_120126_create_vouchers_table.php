<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('vouchers', function (Blueprint $table) {
            $table->id();
            $table->string('serial_number')->unique();
            $table->text('description')->nullable();
            $table->decimal('face_value', 10, 2);
            $table->dateTime('valid_from');
            $table->dateTime('expires_at');
            $table->enum('status', ['ACTIVE', 'USED', 'EXPIRED']);
            $table->string('pin_hash');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('vouchers');
    }
};
