<?php

namespace App\Enums;

enum TransactionStatus: string
{
    case PENDING = 'PENDING';
    case COMPLETED = 'COMPLETED';
    case FAILED = 'FAILED';
    case CANCELED = 'CANCELED';
    public function label(): string
    {
        return match ($this) {
            self::PENDING => __('enum-label.pending'),
            self::COMPLETED => __('enum-label.completed'),
            self::FAILED => __('enum-label.failed'),
            self::CANCELED => __('enum-label.canceled'),
        };
    }



   
}
