<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Level extends Model
{
    protected $fillable = ['name', 'stage_id'];

    protected $casts = [
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'stage_id' => 'integer',
    ];

    public function stage()
    {
        return $this->belongsTo(Stage::class);
    }

    public function subjects()
    {
        return $this->hasMany(Subject::class);
    }

    public function profiles()
    {
        return $this->hasMany(Profile::class);
    }
}
