<?php

namespace App\Http\Controllers\Api\V1\Auth;

use App\Http\Controllers\Controller;
use App\Http\Requests\OtpRequest;
use App\Jobs\SendWhatsAppMessageJob;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;
use Carbon\Carbon;


class AuthController extends Controller
{
    /**
     * Send OTP (One-Time Password) to user's phone number via requested method 'WhatsApp for now'
     * 
     * This function handles the OTP request process with the following features:
     * - Validates the request data (phone number, method and country code)
     * - Implements a 30-second cooldown to prevent spam requests
     * - Generates a random 4-digit OTP (1000-9999)
     * - Sends OTP via WhatsApp using a background job
     * - Stores OTP in cache for 2 minutes for verification
     * - Stores cooldown period in cache for 30 seconds
     * 
     * @param OtpRequest $request - Validated request containing phone_number, method and country code
     * @return JsonResponse - Success/error response with appropriate HTTP status codes
     */
    public function sendOtp(OtpRequest $request): JsonResponse
    {
        try {
            $validatedData = $request->validated();

            $cooldownCacheKey = 'otp_cooldown_' . $validatedData['phone_number'];

            if (Cache::has($cooldownCacheKey)) {
                $expiresAt = Cache::get($cooldownCacheKey);
                $remainingTime = Carbon::now()->diffInSeconds(Carbon::parse($expiresAt));

                return response()->json([
                    'success' => false,
                    'message' => "Please wait before requesting another OTP code",
                    'data' => [
                        'remaining' => (int) $remainingTime,
                        'cooldown_period' => 30,
                        ]
                ], 429);
            }

            $otp = rand(1000, 9999); // Generate a random OTP

            if ($validatedData['method'] == 'whatsapp') {
                SendWhatsAppMessageJob::dispatch(
                    $validatedData['phone_number'],
                    $otp // Pass the OTP as a variable to the WhatsApp message Job
                );
            } else {
                return response()->json([
                    'success' => false,
                    'message' => 'Unverified Method',
                    'data' => [
                        'method' => $validatedData['method']
                        ]
                    ], 422
                );
            }

            // Store OTP in cache for 2 minutes
            $otpCacheKey = 'otp_' . $validatedData['phone_number'];
            Cache::put($otpCacheKey, $otp, now()->addMinutes(2));

            // Store Cooldown time which is 30 seconds
            $expiresAt = Carbon::now()->addSeconds(30);
            Cache::put($cooldownCacheKey, $expiresAt, 30);
            
            return response()->json([
                'success' => true,
                'message' => 'OTP sent successfully',
                'data' => [
                    "expires_at" => now()->addMinutes(2)->toISOString(),
                    "method_used" => $validatedData['method']
                    ],
                ], 200
            );
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to send OTP',
                'data' => [
                    'error' => $e->getMessage(),
                    'error_code' => $e->getCode()
                    ]
                ], 500
            );
        }
    }

    /**
     * Verify the OTP code entered by the user
     * 
     * This function validates the OTP verification process:
     * - Validates phone number and OTP code from request
     * - Automatically converts Libyan phone numbers to '+218' if starts with '0'
     * - Retrieves stored OTP from cache using phone number as key
     * - Compares entered OTP with stored OTP (case-insensitive string comparison)
     * - Clears OTP from cache after successful verification
     * - Returns appropriate success/error responses
     * 
     * @param Request $request - Request containing phone_number and otp_code
     * @return JsonResponse - Success/error response with appropriate HTTP status codes
     */
    public function verifyOtp(Request $request): JsonResponse
    {
        try {
            $validatedData = $request->validate([
                'phone_number' => 'required|string|unique:users,phone_number',
                'otp_code' => 'required|string',
            ]);
            $phoneNumber = $validatedData['phone_number'];

            // If phone number starts with '0', replace it with '+218'
            if ($phoneNumber && str_starts_with($phoneNumber, '0')) {
                $phoneNumber = '+218' . substr($phoneNumber, 1);
            }

            $cacheKey = 'otp_' . $phoneNumber;
            $storedOtp = Cache::get($cacheKey);
            
            if (!$storedOtp) {
                return response()->json([
                    'success' => false,
                    'message' => 'No OTP request found for this phone number or OTP expired',
                    'data' => [
                        'phone' => $phoneNumber,
                        ]
                ], 400);
            }

            if ((string) $storedOtp !== (string) $validatedData['otp_code']) {
                return response()->json([
                    'success' => false,
                    'message' => 'Invalid OTP',
                    'data' => [
                        'phone' => $phoneNumber,
                        ],
                    ], 400
                );
            }

            // Clear the OTP from cache after successful verification
            Cache::forget($cacheKey);

            return response()->json([
                'success' => true, 
                'message' => 'OTP Verified',
                'data' => [
                    'phone_number' => $phoneNumber,
                    'verified_at' => now()->toISOString(),
                    ],
                ], 200
            );

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to Verify OTP', 
                'data' => [
                    'error' => $e->getMessage(),
                    'error_code' => $e->getCode(),
                    ]
                ], 500
            );
        }
    }
}