<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Facades\Storage;
use App\Http\Resources\UserResource;
use App\Http\Resources\TopicResource;
use App\Http\Resources\LessonResource;

class ContentResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'title' => $this->title,
            'description' => $this->description,
            'file_path' => Storage::url($this->file_path),
            'file_type' => $this->file_type,
            'access_type' => $this->access_type->value,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
            'uploaded_by' => new UserResource($this->whenLoaded('uploadedBy')),
            'topic' => new TopicResource($this->whenLoaded('topic')),
            'lesson' => new LessonResource($this->whenLoaded('lesson')),
        ];
    }
}
