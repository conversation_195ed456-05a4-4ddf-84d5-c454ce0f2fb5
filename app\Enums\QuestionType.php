<?php

namespace App\Enums;

enum QuestionType: string
{               
    case MULTIPLE_CHOICE = 'MULTIPLE_CHOICE';
    case TRUE_FALSE= 'TRUE_FALSE';
    case SHORT_ANSWER = 'SHORT_ANSWER';
    case ESSAY = 'ESSAY';

    public function label(): string
    {
        return match($this) {
            self::MULTIPLE_CHOICE =>__('enum-label.multiple_choice'),
            self::TRUE_FALSE =>__('enum-label.true_false'),
            self::SHORT_ANSWER =>__('enum-label.short_answer'),
            self::ESSAY =>__('enum-label.essay'),
        };
    }

    

  
}
