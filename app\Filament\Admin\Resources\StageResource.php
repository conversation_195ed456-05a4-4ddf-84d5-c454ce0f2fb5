<?php

namespace App\Filament\Admin\Resources;

use App\Enums\StageType;
use App\Filament\Admin\Resources\StageResource\Pages;
use App\Filament\Admin\Resources\StageResource\RelationManagers;
use App\Models\Stage;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Columns\ToggleColumn;
use Filament\Tables\Table;

class StageResource extends Resource
{
    protected static ?string $model = Stage::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    public static function getModelLabel(): string
    {
        return __('filament-panels.stages.single');
    }
    public static function getPluralModelLabel(): string
    {
        return __('filament-panels.stages.title');
    }
    public static function getNavigationGroup(): ?string
    {
        return __('filament-panels.groups.courses');
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make()->schema([
                Forms\Components\TextInput::make('name')
                ->label(__('filament-panels.fields.name'))
                ->required()
                ->validationMessages([
                    'required' => __('validation.required'),
                    'max' => __('validation.max.string'),
                ])
                ->maxLength(200),

             

            Forms\Components\Select::make('type')
                ->label(__('filament-panels.fields.type'))
                ->options([
                    StageType::ACADEMIC->value =>StageType::ACADEMIC->label(),
                    StageType::PROFESSIONAL->value =>StageType::PROFESSIONAL->label(),
                ])
                ->required()
                ->validationMessages([
                    'required' => __('validation.required'),
                    'max' => __('validation.max.string'),
                ])
              
                ])
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('id')->sortable(),
                Tables\Columns\TextColumn::make('name')
                    ->label(__('filament-panels.fields.name'))
                    ->sortable()
                    ->searchable(),
                    
                Tables\Columns\TextColumn::make('type')
                    ->formatStateUsing(fn ($state): string => $state->label())
                    ->label(__('filament-panels.fields.type'))
                    ->sortable()
                    ->badge()
                    ->colors([
                        StageType::ACADEMIC->value => 'success',
                        StageType::PROFESSIONAL->value => 'danger',
                    ]),
                ToggleColumn::make('is_active')
                    ->label(__('filament-panels.fields.is_active'))
                    ->toggleable(),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime('Y-m-d H:i')
                    ->label(__('filament-panels.fields.created_at')) 
                     ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('type')->label(__('filament-panels.fields.type'))
                    ->options([
                        StageType::ACADEMIC->value => StageType::ACADEMIC->label(),
                        StageType::PROFESSIONAL->value => StageType::PROFESSIONAL->label(),
                    ]),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make()
                ->visible(fn (\App\Models\Stage $record) => $record->levels()->doesntExist()),

            ])
            ->bulkActions([
            ])
            ->emptyStateIcon('heroicon-o-rectangle-stack')
            ->emptyStateHeading(__('filament-panels.stages.empty.title') ?? 'No stages yet')
            ->emptyStateDescription(__('filament-panels.stages.empty.desc') ?? 'Create a stages to get started.')
            ->emptyStateActions([
                Tables\Actions\CreateAction::make(),
            ])

            ->defaultSort('id', 'asc');
    }

    

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListStages::route('/'),
            'create' => Pages\CreateStage::route('/create'),
            'edit' => Pages\EditStage::route('/{record}/edit'),
        ];
    }
}
