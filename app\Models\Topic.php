<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Topic extends Model
{
    protected $fillable = ['name'];

    protected $casts = [
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    public function lessons()
    {
        return $this->belongsToMany(Lesson::class, 'topic_lessons', 'topic_id', 'lesson_id');
    }

    public function contents()
    {
        return $this->hasMany(Content::class);
    }

    public function questions()
    {
        return $this->hasMany(Question::class);
    }
}
