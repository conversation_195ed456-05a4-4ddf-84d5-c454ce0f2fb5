<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Assessment extends Model
{
   
    protected $fillable = ['title', 'description', 'created_by'];

    protected $casts = [
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'created_by' => 'integer',
    ];

    public function questions()
    {
        return $this->belongsToMany(Question::class, 'assessment_questions', 'assessment_id', 'question_id');
    }

    public function studentAssessments()
    {
        return $this->hasMany(StudentAssessment::class);
    }
    public function createdBy()
    {
        return $this->belongsTo(User::class, 'created_by');
    }
}
