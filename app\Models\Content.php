<?php

namespace App\Models;

use App\Enums\AccessType;
use Illuminate\Database\Eloquent\Model;

class Content extends Model
{
    protected $fillable = [
        'title', 'description', 'file_path', 'file_type',
        'uploaded_by', 'topic_id', 'lesson_id', 'access_type'
    ];

    protected $casts = [
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'uploaded_by' => 'integer',
        'topic_id' => 'integer',
        'lesson_id' => 'integer',
        'access_type' => AccessType::class,
    ];

    public function uploadedBy()
    {
        return $this->belongsTo(User::class, 'uploaded_by');
    }

    public function topic()
    {
        return $this->belongsTo(Topic::class);
    }

    public function lesson()
    {
        return $this->belongsTo(Lesson::class);
    }
    
    public function subscriptionPlans()
    {
        return $this->belongsToMany(SubscriptionPlan::class, 'content_subscription_plan', 'content_id', 'subscription_plan_id');
    }
    public function ratings()
    {
        return $this->hasMany(ContentRate::class);
    }
    public function addons()
    {
        return $this->belongsToMany(Addon::class, 'content_addons', 'content_id', 'addon_id');
    }

}
