<?php

namespace App\Enums;

enum SubscriptionStatus: string
{
    case ACTIVE = 'ACTIVE';
    case PENDING = 'PENDING';   
    case CANCELED = 'CANCELED';
    case EXPIRED = 'EXPIRED';
    
    public function label(): string
    {
        return match ($this) {
            self::ACTIVE => __('enum-label.active'),
            self::PENDING => __('enum-label.pending'),
            self::CANCELED => __('enum-label.canceled'),
            self::EXPIRED => __('enum-label.expired'),
        };
    }


   
}
