<?php

namespace App\Models;

use App\Enums\DifficultyLevel;
use App\Enums\QuestionType;
use Illuminate\Database\Eloquent\Model;

class Question extends Model
{
    protected $fillable = [
        'question_text', 'question_type', 'topic_id', 'difficulty_level', 'created_by'
    ];

    protected $casts = [
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'created_by' => 'integer',
        'topic_id' => 'integer',
        'question_type' => QuestionType::class,
        'difficulty_level'=>DifficultyLevel::class
    ];

    public function topic()
    {
        return $this->belongsTo(Topic::class);
    }

    public function answers()
    {
        return $this->hasMany(Answer::class);
    }

    public function assessments()
    {
        return $this->belongsToMany(Assessment::class, 'assessment_questions', 'question_id', 'assessment_id');
    }

    public function studentAnswers()
    {
        return $this->hasMany(StudentAnswer::class);
    }
    
    public function createdBy()
    {
        return $this->belongsTo(User::class, 'created_by');
    }
}
