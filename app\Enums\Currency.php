<?php

namespace App\Enums;

enum Currency: string
{
    case USD = 'USD';
    case EUR = 'EUR';
    case LYD = 'LYD';
    
    public function label(): string
    {
        return match ($this) {
            self::USD => __('enum-label.usd'),
            self::EUR => __('enum-label.eur'),
            self::LYD => __('enum-label.lyd'),
        };
    }

    public function symbol(): string
    {
        return match ($this) {
            self::USD => '$',
            self::EUR => '€',
            self::LYD => 'د.ل',
        };
    }
}
