<?php

namespace Database\Seeders;

use App\Models\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;

class UserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        //  Create Roles
        $roles = ['admin', 'super_admin', 'teacher', 'student'];
        foreach ($roles as $role) {
            \Spatie\Permission\Models\Role::firstOrCreate(['name' => $role]);
        }

        //  Create Users
        $users = [
            [
                'name' => 'Admin User',
                'email' => '<EMAIL>',
                'password' => Hash::make('password'),
                'role' => 'admin',
                'phone_number' => '91234567890',
            ],
            [
                'name' => 'Teacher User',
                'email' => '<EMAIL>',
                'password' => Hash::make('password'),
                'role' => 'teacher',
                'phone_number' => '91234567890',
            ],
            [
                'name' => 'Student User',
                'email' => '<EMAIL>',
                'password' => Hash::make('password'),
                'role' => 'student',
                'phone_number' => '91234567890',
            ],
            [
                'name' => 'Super Admin User',
                'email' => '<EMAIL>',
                'password' => Hash::make('password'),
                'role' => 'super_admin',
                'phone_number' => '91234567891',
            ],
        ];

        foreach ($users as $u) {
            $user = User::firstOrCreate(
                ['email' => $u['email']],
                [
                    'name' => $u['name'],
                    'password' => $u['password'],
                    'phone_number' => $u['phone_number'],
                ]
            );

            // Assign role
            $user->assignRole($u['role']);
        }
    }
}
