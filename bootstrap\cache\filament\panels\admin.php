<?php return array (
  'livewireComponents' => 
  array (
    'app.filament.admin.resources.level-resource.pages.create-level' => 'App\\Filament\\Admin\\Resources\\LevelResource\\Pages\\CreateLevel',
    'app.filament.admin.resources.level-resource.pages.edit-level' => 'App\\Filament\\Admin\\Resources\\LevelResource\\Pages\\EditLevel',
    'app.filament.admin.resources.level-resource.pages.list-levels' => 'App\\Filament\\Admin\\Resources\\LevelResource\\Pages\\ListLevels',
    'app.filament.admin.resources.stage-resource.pages.create-stage' => 'App\\Filament\\Admin\\Resources\\StageResource\\Pages\\CreateStage',
    'app.filament.admin.resources.stage-resource.pages.edit-stage' => 'App\\Filament\\Admin\\Resources\\StageResource\\Pages\\EditStage',
    'app.filament.admin.resources.stage-resource.pages.list-stages' => 'App\\Filament\\Admin\\Resources\\StageResource\\Pages\\ListStages',
    'filament.pages.dashboard' => 'Filament\\Pages\\Dashboard',
    'filament.widgets.account-widget' => 'Filament\\Widgets\\AccountWidget',
    'filament.livewire.database-notifications' => 'Filament\\Livewire\\DatabaseNotifications',
    'filament.pages.auth.edit-profile' => 'Filament\\Pages\\Auth\\EditProfile',
    'filament.livewire.global-search' => 'Filament\\Livewire\\GlobalSearch',
    'filament.livewire.notifications' => 'Filament\\Livewire\\Notifications',
    'filament.pages.auth.login' => 'Filament\\Pages\\Auth\\Login',
    'filament.pages.auth.password-reset.request-password-reset' => 'Filament\\Pages\\Auth\\PasswordReset\\RequestPasswordReset',
    'filament.pages.auth.password-reset.reset-password' => 'Filament\\Pages\\Auth\\PasswordReset\\ResetPassword',
    'bezhan-salleh.filament-shield.resources.role-resource.pages.list-roles' => 'BezhanSalleh\\FilamentShield\\Resources\\RoleResource\\Pages\\ListRoles',
    'bezhan-salleh.filament-shield.resources.role-resource.pages.create-role' => 'BezhanSalleh\\FilamentShield\\Resources\\RoleResource\\Pages\\CreateRole',
    'bezhan-salleh.filament-shield.resources.role-resource.pages.view-role' => 'BezhanSalleh\\FilamentShield\\Resources\\RoleResource\\Pages\\ViewRole',
    'bezhan-salleh.filament-shield.resources.role-resource.pages.edit-role' => 'BezhanSalleh\\FilamentShield\\Resources\\RoleResource\\Pages\\EditRole',
  ),
  'clusters' => 
  array (
  ),
  'clusteredComponents' => 
  array (
  ),
  'clusterDirectories' => 
  array (
  ),
  'clusterNamespaces' => 
  array (
  ),
  'pages' => 
  array (
    0 => 'Filament\\Pages\\Dashboard',
  ),
  'pageDirectories' => 
  array (
    0 => 'C:\\xampp\\htdocs\\work\\new-addrus\\app\\Filament/Pages',
  ),
  'pageNamespaces' => 
  array (
    0 => 'App\\Filament\\Pages',
  ),
  'resources' => 
  array (
    'C:\\xampp\\htdocs\\work\\new-addrus\\app\\Filament\\Admin\\Resources\\LevelResource.php' => 'App\\Filament\\Admin\\Resources\\LevelResource',
    'C:\\xampp\\htdocs\\work\\new-addrus\\app\\Filament\\Admin\\Resources\\StageResource.php' => 'App\\Filament\\Admin\\Resources\\StageResource',
    0 => 'BezhanSalleh\\FilamentShield\\Resources\\RoleResource',
  ),
  'resourceDirectories' => 
  array (
    0 => 'C:\\xampp\\htdocs\\work\\new-addrus\\app\\Filament/Admin/Resources',
  ),
  'resourceNamespaces' => 
  array (
    0 => 'App\\Filament\\Admin\\Resources',
  ),
  'widgets' => 
  array (
    0 => 'Filament\\Widgets\\AccountWidget',
  ),
  'widgetDirectories' => 
  array (
    0 => 'C:\\xampp\\htdocs\\work\\new-addrus\\app\\Filament/Widgets',
  ),
  'widgetNamespaces' => 
  array (
    0 => 'App\\Filament\\Widgets',
  ),
);