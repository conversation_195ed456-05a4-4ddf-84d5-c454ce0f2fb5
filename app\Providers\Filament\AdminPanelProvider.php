<?php

namespace App\Providers\Filament;

use App\Http\Middleware\SetLocale;
use <PERSON>zhanSalleh\FilamentShield\FilamentShieldPlugin;

use Filament\Http\Middleware\Authenticate;
use Filament\Http\Middleware\AuthenticateSession;
use Filament\Http\Middleware\DisableBladeIconComponents;
use Filament\Http\Middleware\DispatchServingFilamentEvent;
use Filament\Navigation\MenuItem;
use Filament\Pages;
use Filament\Panel;
use Filament\PanelProvider;
use Filament\Support\Colors\Color;
use Filament\Support\Enums\MaxWidth;
use Filament\Widgets;

use Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse;
use Illuminate\Cookie\Middleware\EncryptCookies;
use Illuminate\Foundation\Http\Middleware\VerifyCsrfToken;
use Illuminate\Routing\Middleware\SubstituteBindings;
use Illuminate\Session\Middleware\StartSession;
use Illuminate\View\Middleware\ShareErrorsFromSession;

/**
 * AdminPanelProvider
 *
 * This class configures the Filament admin panel for the application.
 * It sets up the panel's appearance, authentication, middleware, resources,
 */
class AdminPanelProvider extends PanelProvider
{
    /**
     * Configure the admin panel
     *
     * This method defines all the configuration for the Filament admin panel
     * including branding, authentication, middleware stack, and feature settings.
     *
     */
    public function panel(Panel $panel): Panel
    {
        return $panel
            // Basic panel configuration
            ->default() // Set this as the default panel
            ->id('admin') // Panel identifier
            ->path('addrus-admin') // URL path for accessing the panel
            ->login() // Use custom login page

            // Theme and branding configuration
            ->colors([
                'primary' => Color::hex('#1E78EB'), // Primary brand color (blue)
                'secondary' => Color::hex('#FFD836'), // Secondary brand color (yellow)
                'success' => [
                    '500' => '#22c55e', // Success color (green)
                ],
            ])
            
            ->brandLogo(asset('images/logo.png')) // addrus logo
            ->brandLogoHeight('4rem') // Logo height
            ->brandName('ادرس') // Brand name 
            ->font('Inter')

            // Layout and UI configuration
            ->maxContentWidth(MaxWidth::Full) // Use full width layout
            ->sidebarCollapsibleOnDesktop() // Allow sidebar collapse on desktop

            // Authentication features
            ->passwordReset() // Enable password reset functionality
            ->profile() // Enable user profile management

            // User menu customization
            ->userMenuItems([
                // Language switcher menu item
                MenuItem::make()
                    ->label(fn(): string => app()->getLocale() === 'ar' ? 'English' : 'العربية')
                    ->url(fn(): string => '/switch-lang/' . (app()->getLocale() === 'ar' ? 'en' : 'ar'))
                    ->icon('heroicon-m-language'),
            ])

            // Database features
            ->databaseNotifications() // Enable database-stored notifications
            ->databaseTransactions() // Enable database transaction support

            // Application features
            ->spa() // Enable Single Page Application mode
            ->authGuard('web') // Use 'web' authentication guard
            ->authPasswordBroker('users') // Use 'users' password broker
            ->globalSearchKeyBindings(['command+k', 'ctrl+k']) // Global search shortcuts

            // Resource and page discovery
            ->discoverResources(in: app_path('Filament/Admin/Resources'), for: 'App\\Filament\\Admin\\Resources')
            ->discoverPages(in: app_path('Filament/Pages'), for: 'App\\Filament\\Pages')
            ->pages([
                Pages\Dashboard::class,
            ])

            // Widget configuration
            ->discoverWidgets(in: app_path('Filament/Widgets'), for: 'App\\Filament\\Widgets')
            ->widgets([
                Widgets\AccountWidget::class, // User account widget
            ])

            // Plugin registration
            ->plugins([
                
              FilamentShieldPlugin::make(),// Role & permission management
                
            ])

            // Global middleware stack
            ->middleware([
                EncryptCookies::class,
                AddQueuedCookiesToResponse::class, 
                StartSession::class, 
                AuthenticateSession::class, 
                ShareErrorsFromSession::class, 
                VerifyCsrfToken::class, 
                SubstituteBindings::class, 
                DisableBladeIconComponents::class, 
                DispatchServingFilamentEvent::class, 
                SetLocale::class, 
            ])

            // Authentication-specific middleware
            ->authMiddleware([
                Authenticate::class, 
              
            ]);
    }
}
