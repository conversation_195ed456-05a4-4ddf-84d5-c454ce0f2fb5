<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class OtpRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'phone_number' => 'required|string|regex:/^\+?[1-9]\d{1,14}$/|unique:users,phone_number',
            'method' => 'required|string',
            'country_code' => 'required|string',
        ];
    }
    /**
     * Get the error messages for the defined validation rules.
     */
    public function messages(): array
    {   
        return [
            'phone_number.required' => 'The phone number is required',
            'phone_number.string' => 'The phone number must be a string',
            'phone_number.regex' => 'The phone number format is invalid',
            'method.required' => 'The method is required',
            'method.string' => 'The method must be a string',
            'country_code.required' => 'The country code is required',
            'country_code.string' => 'The country code must be a string',
            'phone_number.unique' => 'The phone_number number is already registered',
        ];
    }
    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation()
    {
        $phone_number = $this->input('phone_number');

        // If phone_number starts with '0', replace it with '+218'
        if ($phone_number && str_starts_with($phone_number, '0')) {
            $phone_number = '+218' . substr($phone_number, 1);
            // Remove any duplicate '+' if country code already has it
            $phone_number = preg_replace('/^\+{2,}/', '+', $phone_number);
            $this->merge([
                'phone_number' => $phone_number,
            ]);
        }
    }
}
